# Test configuration for integration tests
# This file provides default values for required environment variables during testing

app:
  apikey: test-api-key
  multitenant:
    enabled: false
    key: test-multitenant-key
    url: http://localhost:3000
    transitKey: test-transit-key
    idHeader: X-ORIGINAL-HOST
    applicationName: auth-server
    jpa:
      hikari:
        maximum-pool-size: 10
        idle-timeout: 120000
        minimum-idle: 2
        connection-timeout: 30000
        validation-timeout: 120000
      mode: full
  version: 1.0.0-SNAPSHOT
  build: test-build
  date: 1640995200000
  authorization:
    context-path: /api/v1
    autoconfigure:
      service: true
  claims:
    context-path: /api/v1
    autoconfigure:
      service: true
  business:
    context-path: /api/v1
    autoconfigure:
      services: true

# Disable unnecessary features for testing
spring:
  datasource:
    properties:
      trustServerCertificate: true
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
  application:
    name: auth-server-test
    # Flyway configuration for tests - let Flyway handle schema creation
  flyway:
    enabled: true
    clean-disabled: false
    baseline-on-migrate: true
    validate-on-migrate: false

# Logging configuration for tests
logging:
  level:
    root: WARN
    com.vusecurity: INFO
    org.testcontainers: INFO
    org.springframework.boot.test: INFO



# Hibernate configuration for tests - use none to let Flyway handle schema
hibernate:
  show_sql: false
  hbm2ddl.auto: none
  connection.useUnicode: true
  connection.characterEncoding: UTF-8
  connection.charSet: UTF-8
  dialect: org.hibernate.dialect.SQLServerDialect
  implicit_naming_strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
  physical_naming_strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

# Management endpoints for tests
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    diskspace:
      enabled: false
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
