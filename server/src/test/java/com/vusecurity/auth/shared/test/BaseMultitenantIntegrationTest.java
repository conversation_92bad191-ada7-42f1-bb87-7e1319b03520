package com.vusecurity.auth.shared.test;

import com.vusecurity.auth.shared.config.TestMultitenantService;
import com.vusecurity.multitenant.test.TestTenantContextManager;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MSSQLServerContainer;

/**
 * Base class for integration tests that want to test the full multitenant
 * system but with TestContainers datasource instead of external service.
 * 
 * This configuration:
 * - Uses shared TestContainers SQL Server setup
 * - Keeps the complex multitenant datasource resolution system
 * - Mocks the MultitenantService to return TestContainers connection details
 * - Allows testing multiple tenants with proper isolation
 * - Useful for testing tenant-specific behavior and datasource creation
 * - Provides automatic database cleanup between tests
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@ExtendWith(TestTenantContextManager.class)
@Import({TestMultitenantService.class, BaseMultitenantIntegrationTest.TestContainerConfiguration.class})
public abstract class BaseMultitenantIntegrationTest {

    @Autowired
    private TestDatabaseCleaner databaseCleaner;

    @BeforeAll
    static void setupContainerLogging() {
        SharedTestContainers.enableContainerLogging();
    }

    @BeforeEach
    void cleanupDatabase() {
        // Clean database before each test for isolation
        databaseCleaner.smartCleanDatabase();
    }

    @DynamicPropertySource
    static void configureTestProperties(DynamicPropertyRegistry registry) {
        // Use shared container configuration
        SharedTestContainers.configureProperties(registry);
        
        // Override specific properties for multitenant mode
        registry.add("app.multitenant.test.single-datasource", () -> "false");
        registry.add("app.multitenant.test.mock-service", () -> "true");
    }

    /**
     * Test multiple tenants with proper context switching
     */
    protected void testWithMultipleTenants(String tenant1, String tenant2, 
                                         Runnable tenant1Action, Runnable tenant2Action) {
        // Test tenant 1
        TestTenantContextManager.withTenantContext(tenant1, tenant1Action);
        
        // Test tenant 2
        TestTenantContextManager.withTenantContext(tenant2, tenant2Action);
    }

    /**
     * Helper method to execute code within a specific tenant context
     */
    protected void withTenantContext(String tenantId, Runnable action) {
        TestTenantContextManager.withTenantContext(tenantId, action);
    }

    /**
     * Helper method to get current tenant
     */
    protected String getCurrentTenant() {
        return TestTenantContextManager.getCurrentTenant();
    }

    /**
     * Helper method to set tenant context
     */
    protected void setTenantContext(String tenantId) {
        TestTenantContextManager.setTenantContext(tenantId);
    }

    /**
     * Configuration to provide the TestContainers SQL Server as a Spring bean
     * so it can be injected into the TestMultitenantService
     */
    @TestConfiguration
    static class TestContainerConfiguration {

        // No longer needed with H2 database
    }
}
