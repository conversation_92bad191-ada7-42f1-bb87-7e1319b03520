package com.vusecurity.auth.shared.config;

import com.vusecurity.multitenant.MultitenantConfiguration;
import com.vusecurity.multitenant.MultitenantService;
import com.vusecurity.multitenant.model.ConnectionProvider;
import com.vusecurity.multitenant.model.Subscription;
import com.vusecurity.multitenant.model.props.PropertiesConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;
import org.testcontainers.containers.MSSQLServerContainer;

import java.util.Collections;
import java.util.List;

/**
 * Test configuration that provides mock implementations of multitenant components
 * when app.multitenant.enabled=false
 */
@TestConfiguration
public class TestMultitenantService {

    private static final Logger logger = LoggerFactory.getLogger(TestMultitenantService.class);

    /**
     * Test-specific MultitenantService bean that replaces the default one
     * when multitenant is disabled (app.multitenant.enabled=false)
     */
    @Bean
    public MultitenantService multitenantService(MSSQLServerContainer<?> sqlContainer) {
        return new TestMultitenantServiceImpl(sqlContainer);
    }

    /**
     * Test-specific MultitenantConfiguration bean for SecurityConfig dependency
     */
    @Bean
    public MultitenantConfiguration multitenantConfiguration() {
        MultitenantConfiguration config = new MultitenantConfiguration();
        config.setKey("test-key");
        config.setUrl("http://localhost:3000");
        config.setTransitKey("test-transit-key");
        config.setIdHeader("X-ORIGINAL-HOST");
        config.setApplicationName("auth-server");
        return config;
    }

    /**
     * Test-specific DataSource that replaces the multitenant datasource
     */
    @Bean
    public DataSource dataSource(MSSQLServerContainer<?> sqlContainer) {
        // Create a simple DataSource from the TestContainers connection details
        org.springframework.boot.jdbc.DataSourceBuilder<?> builder = org.springframework.boot.jdbc.DataSourceBuilder.create();
        builder.url(sqlContainer.getJdbcUrl());
        builder.username(sqlContainer.getUsername());
        builder.password(sqlContainer.getPassword());
        builder.driverClassName("com.microsoft.sqlserver.jdbc.SQLServerDriver");

        return builder.build();
    }

    /**
     * Test-specific EntityManagerFactory that replaces the multitenant one
     */
    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(DataSource dataSource) {
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("com.vusecurity.**");

        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);

        Properties properties = new Properties();
        properties.setProperty("hibernate.hbm2ddl.auto", "none");
        properties.setProperty("hibernate.dialect", "org.hibernate.dialect.SQLServerDialect");
        properties.setProperty("hibernate.show_sql", "false");
        properties.setProperty("hibernate.format_sql", "false");
        em.setJpaProperties(properties);

        return em;
    }

    /**
     * Test-specific TransactionManager
     */
    @Bean
    public PlatformTransactionManager transactionManager(LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory.getObject());
    }

    /**
     * Test-specific JdbcTemplate that replaces multitenantJdbcTemplate
     */
    @Bean
    public JdbcTemplate multitenantJdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * Implementation of MultitenantService for tests
     */
    private static class TestMultitenantServiceImpl extends MultitenantService {

        private final MSSQLServerContainer<?> sqlContainer;

        public TestMultitenantServiceImpl(MSSQLServerContainer<?> sqlContainer) {
            super(null); // Pass null configuration since we're overriding everything
            this.sqlContainer = sqlContainer;
            logger.info("TestMultitenantService initialized with container: {}", sqlContainer.getJdbcUrl());
        }

        @Override
        public ConnectionProvider getConnectionReadOnly(String tenant) {
            logger.debug("TestMultitenantService.getConnectionReadOnly() called for tenant: {}", tenant);
            return createTestConnectionProvider(tenant, true);
        }

        @Override
        public ConnectionProvider getConnection(String tenant, String applicationName) {
            logger.debug("TestMultitenantService.getConnection() called for tenant: {} applicationName: {}", tenant, applicationName);
            return createTestConnectionProvider(tenant, false);
        }

        @Override
        public ConnectionProvider getConnection(String tenant) {
            logger.debug("TestMultitenantService.getConnection() called for tenant: {}", tenant);
            return createTestConnectionProvider(tenant, false);
        }

        @Override
        public List<Subscription> getSubscriptions() {
            logger.debug("TestMultitenantService.getSubscriptions() called - returning empty list");
            return Collections.emptyList();
        }

        @Override
        public String getProperty(String key) {
            logger.debug("TestMultitenantService.getProperty() called for key: {} - returning null", key);
            return null;
        }

        @Override
        public PropertiesConfig getProperties(String tenant) {
            logger.debug("TestMultitenantService.getProperties() called for tenant: {} - returning empty config", tenant);
            PropertiesConfig config = new PropertiesConfig();
            return config;
        }

        /**
         * Creates a ConnectionProvider that points to the TestContainers SQL Server instance
         */
        private ConnectionProvider createTestConnectionProvider(String tenant, boolean readOnly) {
            ConnectionProvider provider = new ConnectionProvider();
            provider.setAlias("test-datasource");
            provider.setDatasourceUrl(sqlContainer.getJdbcUrl());
            provider.setUser(sqlContainer.getUsername());
            provider.setPassword(sqlContainer.getPassword());
            provider.setReadOnly(readOnly);

            logger.debug("Created test ConnectionProvider for tenant '{}': url={}, user={}, readOnly={}",
                        tenant, provider.getDatasourceUrl(), provider.getUser(), readOnly);

            return provider;
        }
    }
}
