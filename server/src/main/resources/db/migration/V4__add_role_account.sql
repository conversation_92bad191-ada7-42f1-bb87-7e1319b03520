/*
 * Flyway Migration: V4__add_role_account.sql
 * Purpose: Assign admin role to existing admin account.
 * Idempotent: Uses IF NOT EXISTS checks for SQL Server syntax.
 */

INSERT INTO account_role (role_id, account_id)
SELECT '9FA7A7F6-B8D4-4DB4-A8C7-1642A80E863A', 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
WHERE NOT EXISTS (
    SELECT 1
    FROM account_role
    WHERE role_id = '9FA7A7F6-B8D4-4DB4-A8C7-1642A80E863A'
      AND account_id = 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
)
AND EXISTS (
    SELECT 1
    FROM account
    WHERE id = 'A5F5129E-3901-4F65-9C1C-90F176D7D60F'
);
